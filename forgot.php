<?php
session_start();
include_once '../lib/config.php';
?>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>Welcome To "TETHER50" Affiliate Program: Gaming, Lottery & Staking Earning Platform | Fast, Secure & Transparent</title>
<meta name="viewport"
		content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
<link rel="icon" type="image/png" href="./assets/fav.png" sizes="96x96" />
<link rel="stylesheet" href="./assets/cy/libs/bootstrap/css/bootstrap.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/priority-navigation-master/dist/priority-nav-core.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/swiper/swiper.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap-select-1.14.0/dist/css/bootstrap-select.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/plyr/dist/plyr.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/ion.rangeSlider-master/css/ion.rangeSlider.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/toastr-master/build/toastr.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/main.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/media.css?vs=100">
<style>
.openUserInfo {
	cursor: pointer;
}
.sectionLotteriesItemsSMEmpty {
	min-height: 46px;
}
button.change-pswd-type-link {
	border: 0px;
}
.logo-img img {
  width: 160px;
}
.mobile-panel-top-left .logo-wrapper {
  display: flex;
  max-width: 160px;
}
.field--contact .field-icon::before{background-color:transparent;}
.field--contact  img{margin-top:-10px;}
.hidden { display: none; }
.margin-top { margin-top: 15px; }
</style>

<link rel="shortcut icon" href="favicon.ico" />
</head>

<body class="page page--main">
<div class="page-inner">
<div class="page-progress-block xProgress d-none">
  <div class="page-progress">
    <div class="page-progress__progress xProgressPercent" style="width: 0%;"></div>
  </div>
</div>
<div class="mobile-panel-block">
  <div class="mobile-panel-top-block">
    <div class="mobile-panel-top-left">
      <div class="logo-wrapper"> <a href="index.php" class="logo">
        <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
        </a> </div>
    </div>
    <div class="mobile-panel-top-right">
      <div class="topline-lang-panel-block">
        <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
								aria-expanded="false">
          <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
          <div class="current-lang__text">EN</div>
          </a>
          <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="?lang=default" class="lang-link">
            <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
            <div class="lang-link__text">EN</div>
            </a> </div>
        </div>
      </div>
      <div class="mobile-panel-close-btn-block">
        <button type="button" class="mobile-panel-close-btn"></button>
      </div>
    </div>
  </div>
  <div class="mobile-panel-menu-block">
    <div class="mobile-panel-menu">
      <div class="mobile-auth-panel-block">
        <div class="mobile-auth-panel">
          <div class="mobile-auth-panel-btn-block">
            <a  href='login.php' class="topline-login-btn">
            <div class="topline-login-btn__text">Sign In</div>
            <div class="topline-login-btn__icon"></div>
            </a>
          </div>
          <div class="mobile-auth-panel-btn-block"> <a href="signup.php" class="topline-registration-btn">
            <div class="topline-registration-btn__text">Register</div>
            <div class="topline-registration-btn__icon"></div>
            </a> </div>
        </div>
      </div>
      <div class="mobile-menu">
        <ul>
          <li class="mobile-menu-item mobile-menu-item--games"> <a href="about.php" class="mobile-menu-link mobile-menu-link--games">
            <div class="mobile-menu-link__text"> About Us </div>
            </a> </li>
          <li class="mobile-menu-item mobile-menu-item--lottery"> <a href="index.php#help" class="mobile-menu-link mobile-menu-link--lottery">
            <div class="mobile-menu-link__text"> How It Works </div>
            </a> </li>
          <li class="mobile-menu-item mobile-menu-item--staking"> <a href="game.php" class="mobile-menu-link mobile-menu-link--staking">
            <div class="mobile-menu-link__icon"> <img class="image" src="assets/cy/images/theme/topmenu-link-icon--city.png" alt=""> </div>
            <div class="mobile-menu-link__text"> Gaming  </div>
            </a> </li>

            <li class="mobile-menu-item mobile-menu-item--staking"> <a href="lotteries.php" class="mobile-menu-link mobile-menu-link--staking">
            <div class="mobile-menu-link__icon"> <img class="image" src="assets/cy/images/theme/topmenu-link-icon--city.png" alt=""> </div>
            <div class="mobile-menu-link__text"> Lotteries  </div>
            </a> </li>

            <li class="mobile-menu-item mobile-menu-item--staking"> <a href="staking.php" class="mobile-menu-link mobile-menu-link--staking">
            <div class="mobile-menu-link__icon"> <img class="image" src="assets/cy/images/theme/topmenu-link-icon--city.png" alt=""> </div>
            <div class="mobile-menu-link__text"> Staking  </div>
            </a> </li>

         <li class="mobile-menu-item mobile-menu-item--help-center"> <a href="affiliate-program.php" class="mobile-menu-link mobile-menu-link--affiliate-program">
            <div class="mobile-menu-link__text"> Affiliate Program  </div>
            </a> </li>
          <li class="mobile-menu-item mobile-menu-item--help-center"> <a href="help-center.php" class="mobile-menu-link mobile-menu-link--affiliate-program">
            <div class="mobile-menu-link__text"> Help Center  </div>
            </a> </li>
          <li class="mobile-menu-item mobile-menu-item--affiliate-program"> <a href="contact.php" class="mobile-menu-link mobile-menu-link--news">
            <div class="mobile-menu-link__text"> Contact Us </div>
            </a> </li>


        </ul>
      </div>
    </div>
  </div>
</div>
<header>
  <div class="topline-block-wrapper">
    <div class="topline-block">
      <div class="container">
        <div class="row">
          <div class="col-12">
            <div class="topline">
              <div class="topline-left">
                <div class="logo-wrapper"> <a href="index.php" class="logo">
                  <div class="logo-img"> <img src="assets/logo.png" alt="img"> </div>
                  </a> </div>
                <div class="topmenu-block">
                  <div class="topmenu">
                    <ul>
          <li class="mobile-menu-item mobile-menu-item--games"> <a href="about.php" class="mobile-menu-link mobile-menu-link--games">
            <div class="mobile-menu-link__text"> About Us </div>
            </a> </li>
          <li class="mobile-menu-item mobile-menu-item--lottery"> <a href="index.php#how" class="mobile-menu-link mobile-menu-link--lottery">
            <div class="mobile-menu-link__text"> How It Works </div>
            </a> </li>
          <li class="mobile-menu-item mobile-menu-item--staking"> <a href="game.php" class="mobile-menu-link mobile-menu-link--staking">
            <div class="mobile-menu-link__icon"> <img class="image" src="assets/cy/images/theme/topmenu-link-icon--city.png" alt=""> </div>
            <div class="mobile-menu-link__text"> Gaming  </div>
            </a> </li>

            <li class="mobile-menu-item mobile-menu-item--staking"> <a href="lotteries.php" class="mobile-menu-link mobile-menu-link--staking">
            <div class="mobile-menu-link__icon"> <img class="image" src="assets/cy/images/theme/topmenu-link-icon--city.png" alt=""> </div>
            <div class="mobile-menu-link__text"> Lotteries  </div>
            </a> </li>

            <li class="mobile-menu-item mobile-menu-item--staking"> <a href="staking.php" class="mobile-menu-link mobile-menu-link--staking">
            <div class="mobile-menu-link__icon"> <img class="image" src="assets/cy/images/theme/topmenu-link-icon--city.png" alt=""> </div>
            <div class="mobile-menu-link__text"> Staking  </div>
            </a> </li>

			<li class="mobile-menu-item mobile-menu-item--help-center"> <a href="affiliate-program.php" class="mobile-menu-link mobile-menu-link--affiliate-program">
            <div class="mobile-menu-link__text"> Affiliate Program  </div>
            </a> </li>

		 <li class="mobile-menu-item mobile-menu-item--help-center"> <a href="help-center.php" class="mobile-menu-link mobile-menu-link--affiliate-program">
            <div class="mobile-menu-link__text"> Help Center  </div>
            </a> </li>
          <li class="mobile-menu-item mobile-menu-item--affiliate-program"> <a href="contact.php" class="mobile-menu-link mobile-menu-link--news">
            <div class="mobile-menu-link__text"> Contact Us </div>
            </a> </li>



        </ul>
                  </div>
                </div>
              </div>
              <div class="topline-right">
                <div class="topline-panel-block">
                  <div class="topline-panel">
                    <div class="topline-lang-panel-block">
                      <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
								aria-expanded="false">
                        <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                        <div class="current-lang__text">EN</div>
                        </a>
                        <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="?lang=default" class="lang-link">
                          <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                          <div class="lang-link__text">EN</div>
                          </a> </div>
                      </div>
                    </div>
                    <div class="topline-login-btn-block">
                      <a href='login.php' class="topline-login-btn">
                      <div class="topline-login-btn__text">Sign In</div>
                      <div class="topline-login-btn__icon"></div>
                      </a>
                    </div>
                    <div class="topline-registration-btn-block"> <a href="signup.php" class="topline-registration-btn">
                      <div class="topline-registration-btn__text">Register</div>
                      <div class="topline-registration-btn__icon"></div>
                      </a> </div>
                    <div class="mobile-panel-btn-block">
                      <button type="button" class="mobile-panel-btn"></button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>
<section class="section-registration">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="registration-block">
          <div class="registration">
            <div class="registration-left">
              <div class="registration-feature-items-block">
                <div class="registration-feature-items">
                  <div class="registration-feature-item-wrapper">
                    <div class="registration-feature-item">
                      <div class="registration-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--dice-five.svg" alt=""> </div>
                      <div class="registration-feature-item__descr"> Exciting and fair games and lotteries </div>
                    </div>
                  </div>
                  <div class="registration-feature-item-wrapper">
                    <div class="registration-feature-item">
                      <div class="registration-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--checkerboard.svg" alt=""> </div>
                      <div class="registration-feature-item__descr"> Staking offers with returns of up to 200% </div>
                    </div>
                  </div>
                  <div class="registration-feature-item-wrapper">
                    <div class="registration-feature-item">
                      <div class="registration-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--shield-check.svg" alt=""> </div>
                      <div class="registration-feature-item__descr"> We ensure your security and anonymity </div>
                    </div>
                  </div>
                  <div class="registration-feature-item-wrapper">
                    <div class="registration-feature-item">
                      <div class="registration-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--rocket-launch.svg" alt=""> </div>
                      <div class="registration-feature-item__descr"> Join our generous affiliate program </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="registration-left-bg-image"> <img class="image" src="assets/cy/images/theme/registration-left-image.png" alt=""> </div>
            </div>
            <div class="registration-right">
              <div class="registration-form-block">
                <h2>Forgot Password</h2>
                <form class="registration-form form">
                  <!-- STEP 1: Enter User ID and Email -->
                  <div id="step1">
                    <div class="field-block">
                      <div class="field-title-block">
                        <div class="field-title">User ID <span class="field-required-star">*</span></div>
                      </div>
                      <div class="field field--input field--have-icon field--email">
                        <div class="field-icon"></div>
                        <input id="login_id" type="text" name="login_id" maxlength="20" placeholder="Enter your User ID" autocomplete="off" required>
                      </div>
                    </div>

                    <div class="field-block">
                      <div class="field-title-block">
                        <div class="field-title">Email <span class="field-required-star">*</span></div>
                      </div>
                      <div class="field field--input field--have-icon field--email">
                        <div class="field-icon"></div>
                        <input id="email" type="email" name="email" maxlength="100" placeholder="Enter your Email" autocomplete="off" required>
                      </div>
                    </div>

                    <div class="form-button-block">
                      <button type="button" id="sendOtpBtn" class="green-gr-btn send-btn">
                        <div class="send-btn__text">Send OTP</div>
                        <div class="send-btn__icon"></div>
                      </button>
                    </div>
                    <div id="userDetailsError" class="alert alert-danger hidden margin-top"></div>
                  </div>

                  <!-- STEP 2: OTP Verification -->
                  <div id="step2" class="hidden">
                    <div class="field-block">
                      <div class="field-title-block">
                        <div class="field-title">OTP <span class="field-required-star">*</span></div>
                      </div>
                      <div class="field field--input field--have-icon field--email">
                        <div class="field-icon"></div>
                        <input id="otp" type="text" name="otp" maxlength="6" placeholder="Enter OTP" autocomplete="off" required>
                      </div>
                    </div>

                    <div class="form-button-block">
                      <button type="button" id="verifyOtpBtn" class="green-gr-btn send-btn">
                        <div class="send-btn__text">Verify OTP</div>
                        <div class="send-btn__icon"></div>
                      </button>
                    </div>
                    <div id="otpError" class="alert alert-danger hidden margin-top"></div>
                  </div>

                  <!-- STEP 3: Create New Password -->
                  <div id="step3" class="hidden">
                    <form id="changePasswordForm" action="forgot_model.php?step=3" method="post" novalidate>
                      <div class="field-block">
                        <div class="field-title-block">
                          <div class="field-title">New Password <span class="field-required-star">*</span></div>
                        </div>
                        <div class="field field--input field--have-icon field--password">
                          <div class="field-icon"></div>
                          <input type="password" name="password" placeholder="Enter new password" autocomplete="off" required>
                        </div>
                      </div>

                      <div class="field-block">
                        <div class="field-title-block">
                          <div class="field-title">Confirm New Password <span class="field-required-star">*</span></div>
                        </div>
                        <div class="field field--input field--have-icon field--password">
                          <div class="field-icon"></div>
                          <input type="password" name="confirm_password" placeholder="Confirm new password" autocomplete="off" required>
                        </div>
                      </div>

                      <!-- Pass along the login_id and email for final submission -->
                      <input type="hidden" name="login_id" id="hidden_login_id">
                      <input type="hidden" name="email" id="hidden_email">

                      <div class="form-button-block">
                        <button type="submit" id="finalSubmitBtn" class="green-gr-btn send-btn">
                          <div class="send-btn__text">Submit</div>
                          <div class="send-btn__icon"></div>
                        </button>
                      </div>
                      <div id="changePwdError" class="alert alert-danger hidden margin-top"></div>
                    </form>
                  </div>

                  <div class="form-bottom-note-block">
                    <div class="form-bottom-note">Remember your password? <a href="login.php">Login</a></div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        <div class="registration-mobile-feature-items-block">
          <div class="registration-mobile-feature-items">
            <div class="registration-mobile-feature-item-wrapper">
              <div class="registration-mobile-feature-item">
                <div class="registration-mobile-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--dice-five.svg" alt=""> </div>
                <div class="registration-mobile-feature-item__descr"> Exciting and fair games and lotteries </div>
              </div>
            </div>
            <div class="registration-mobile-feature-item-wrapper">
              <div class="registration-mobile-feature-item">
                <div class="registration-mobile-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--checkerboard.svg" alt=""> </div>
                <div class="registration-mobile-feature-item__descr"> Staking offers with returns of up to 200% </div>
              </div>
            </div>
            <div class="registration-mobile-feature-item-wrapper">
              <div class="registration-mobile-feature-item">
                <div class="registration-mobile-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--shield-check.svg" alt=""> </div>
                <div class="registration-mobile-feature-item__descr"> We ensure your security and anonymity </div>
              </div>
            </div>
            <div class="registration-mobile-feature-item-wrapper">
              <div class="registration-mobile-feature-item">
                <div class="registration-mobile-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--rocket-launch.svg" alt=""> </div>
                <div class="registration-mobile-feature-item__descr"> Join our generous affiliate program </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<footer>
<div class="container">
  <div class="row">
    <div class="col-12">
      <div class="footer-content">
        <div class="row">
          <div class="col-12 col-md-5 col-lg-3 col-xxl-2">
            <div class="footer-content-left">
              <div class="logo-wrapper"> <a href="/" class="logo">
                <div class="logo-img"> <img src="assets/logo.png" alt=""> </div>
                </a> </div>
              <div class="footer-copy"> © 2025 TETHER50 <br>
                All rights reserved </div>
            </div>
          </div>
          <div class="col-12 col-md-7 col-lg-9 col-xxl-10">
            <div class="footer-content-right">
              <div class="footer-menu-blocks">
                <div class="footer-menu-block footer-menu-block--product">
                  <div class="footer-menu-block-inner">
                    <div class="footer-menu-block-title"> Products </div>
                    <div class="footer-menu">
                      <ul>
                        <li class="footer-menu-item"> <a href="game.php" class="footer-menu-link">Games</a> </li>
                        <li class="footer-menu-item"> <a href="game.php" class="footer-menu-link">Gaming API</a> </li>
                        <li class="footer-menu-item"> <a href="lotteries.php" class="footer-menu-link">Lotteries</a> </li>
                        <li class="footer-menu-item"> <a href="staking.php" class="footer-menu-link">Staking </a> </li>
                        <li class="footer-menu-item"> <a href="affiliate-program.php" class="footer-menu-link">Affiliate Program</a> </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="footer-menu-block footer-menu-block--support">
                  <div class="footer-menu-block-inner">
                    <div class="footer-menu-block-title"> Platform </div>
                    <div class="footer-menu">
                      <ul>
                        <li class="footer-menu-item"> <a href="index.php" class="footer-menu-link">Home</a> </li>
                        <li class="footer-menu-item"> <a href="about.php" class="footer-menu-link">About Us</a> </li>
                        <li class="footer-menu-item"> <a href="index.php#how" class="footer-menu-link">How It Works</a> </li>
                        <li class="footer-menu-item"> <a href="help-center.php" class="footer-menu-link">Help Center</a> </li>
                        <li class="footer-menu-item"> <a href="contact.php" class="footer-menu-link">Contact Us</a> </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="footer-menu-block footer-menu-block--terms">
                  <div class="footer-menu-block-inner">
                    <div class="footer-menu-block-title"> Terms & Conditions </div>
                    <div class="footer-menu">
                      <ul>
                        <li class="footer-menu-item"> <a href="terms-of-service.php" class="footer-menu-link">Terms of Service</a> </li>
                        <li class="footer-menu-item"> <a href="risk-disclosure.php" class="footer-menu-link">Risk Disclosure</a> </li>
                        <li class="footer-menu-item"> <a href="responsible-gaming.php" class="footer-menu-link">Responsible Gaming</a> </li>
                        <li class="footer-menu-item"> <a href="privacy-policy.php" class="footer-menu-link">Privacy Policy</a> </li>
                        <li class="footer-menu-item"> <a href="cookie-policy.php" class="footer-menu-link">Cookie Policy</a> </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="footer-menu-block footer-menu-block--contact">
                  <div class="footer-menu-block-inner">
                    <div class="footer-menu-block-title"> Contact details </div>
                    <div class="footer-contact-items-block">
                      <div class="footer-contact-items">
                        <div class="footer-contact-item"> <a href="" class="footer-contact-email-link">
                          <div class="footer-contact-email-link__icon"></div>
                          <div class="footer-contact-email-link__text"> <EMAIL> </div>
                          </a> </div>
                        <div class="footer-contact-item">
                          <div class="footer-contact-address">
                            <div class="footer-contact-address__icon"></div>
                            <div class="footer-contact-address__text"> TETHER LIMITED<br />
                              Company number:<br />
                              13957078 <br />
                              <a href="https://find-and-update.company-information.service.gov.uk/company/13957078">Verify </a></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="footer-copy">  2025 TETHER50 . <br>
                All rights reserved </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </footer>

</div>
<script type="text/javascript" src="./assets/cy/libs/jquery/jquery-3.6.1.min.js?vs=100"></script>
<script type="text/javascript" src="./assets/cy/libs/bootstrap/js/bootstrap.bundle.min.js?vs=100"></script>
<script type="text/javascript" src="./assets/cy/libs/priority-navigation-master/dist/priority-nav.min.js?vs=100"></script>
<script type="text/javascript" src="./assets/cy/libs/swiper/swiper.min.js?vs=100"></script>
<script type="text/javascript" src="./assets/cy/libs/bootstrap-select-1.14.0/dist/js/bootstrap-select.min.js?vs=100"></script>
<script type="text/javascript" src="./assets/cy/libs/plyr/dist/plyr.min.js?vs=100"></script>
<script type="text/javascript" src="./assets/cy/libs/ion.rangeSlider-master/js/ion.rangeSlider.min.js?vs=100"></script>
<script type="text/javascript" src="./assets/cy/libs/toastr-master/build/toastr.min.js?vs=100"></script>
<script type="text/javascript" src="./assets/cy/js/common.js?vs=100"></script>
<script>
  $(document).ready(function(){
    // STEP 1: Send OTP

    $('#sendOtpBtn').click(function(){
      // Clear any previous error messages.
      $('#userDetailsError').addClass('hidden').text('');

      var login_id = $('#login_id').val().trim();
      var email = $('#email').val().trim();

      if (login_id === "" || email === "") {
        $('#userDetailsError').removeClass('hidden').text('Both User ID and Email are required.');
        return;
      }

      $("#sendOtpBtn").attr("disabled", "true");
      // Save the entered values in hidden fields for later submission.
      $('#hidden_login_id').val(login_id);
      $('#hidden_email').val(email);

      // Add debugging
      console.log('Sending OTP request for:', email);

      $.ajax({
        url: 'send_otp.php',
        type: 'POST',
        dataType: 'json',
        data: {
          email: email,
          login_id: login_id
        },
        success: function(response) {
          console.log('OTP Response:', response);
          if (response.success) {
            // Hide the Send OTP button after a successful response.
            $('#sendOtpBtn').hide();
            $('#step2').removeClass('hidden');
            // Show the OTP input and Verify OTP button.
            $('#step2').slideDown();
          } else {
            $("#sendOtpBtn").removeAttr("disabled");
            $('#userDetailsError').removeClass('hidden').text(response.error || 'Failed to send OTP.');
          }
        },
        error: function(xhr, status, error) {
          console.log('AJAX Error:', xhr.responseText, status, error);
          $("#sendOtpBtn").removeAttr("disabled");

          // For testing purposes, if the file doesn't exist, show step 2 anyway
          if (xhr.status === 404) {
            $('#userDetailsError').removeClass('hidden').text('OTP service temporarily unavailable. Proceeding to next step for testing...');
            setTimeout(function() {
              $('#userDetailsError').addClass('hidden');
              $('#sendOtpBtn').hide();
              $('#step2').removeClass('hidden');
              $('#step2').slideDown();
            }, 2000);
          } else {
            $('#userDetailsError').removeClass('hidden').text('Error: ' + error + ' (Status: ' + xhr.status + ')');
          }
        }
      });
    });

    // STEP 2: Verify OTP
    $('#verifyOtpBtn').click(function(){
      $('#otpError').addClass('hidden').text('');
      var otp = $('#otp').val().trim();
      if (otp === "") {
        $('#otpError').removeClass('hidden').text('Please enter the OTP.');
        return;
      }

      $("#verifyOtpBtn").attr("disabled", "true");

      // Add debugging
      console.log('Verifying OTP:', otp);

      $.ajax({
        url: 'verify_otp.php',
        type: 'POST',
        dataType: 'json',
        data: { otp: otp },
        success: function(response) {
          console.log('OTP Verification Response:', response);
          if (response.success) {
            // Hide the Verify OTP button upon successful OTP verification.
            $('#verifyOtpBtn').hide();
            $('#step3').removeClass('hidden');
            // Hide the OTP input section.
            $('#step2').slideUp();
            // Show the password change section.
            $('#step3').slideDown();
          } else {
            $("#verifyOtpBtn").removeAttr("disabled");
            $('#otpError').removeClass('hidden').text(response.error || 'OTP verification failed.');
          }
        },
        error: function(xhr, status, error) {
          console.log('OTP Verification Error:', xhr.responseText, status, error);
          $("#verifyOtpBtn").removeAttr("disabled");

          // For testing purposes, if the file doesn't exist, show step 3 anyway
          if (xhr.status === 404) {
            $('#otpError').removeClass('hidden').text('OTP verification service temporarily unavailable. Proceeding to password reset for testing...');
            setTimeout(function() {
              $('#otpError').addClass('hidden');
              $('#verifyOtpBtn').hide();
              $('#step3').removeClass('hidden');
              $('#step2').slideUp();
              $('#step3').slideDown();
            }, 2000);
          } else {
            $('#otpError').removeClass('hidden').text('Error: ' + error + ' (Status: ' + xhr.status + ')');
          }
        }
      });
    });

    // Client-side validation for the password fields before final submission.
    $('#changePasswordForm').submit(function(e){
      var pwd = $(this).find('input[name="password"]').val();
      var cpwd = $(this).find('input[name="confirm_password"]').val();
      if(pwd !== cpwd){
        e.preventDefault();
        $('#changePwdError').removeClass('hidden').text('Passwords do not match.');
      }
    });
  });
</script>
</body>
</html>
